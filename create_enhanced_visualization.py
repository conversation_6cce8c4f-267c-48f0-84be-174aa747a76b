#!/usr/bin/env python3
"""
优化的点积运算性能可视化脚本
Enhanced visualization script for dot product performance analysis
"""

import matplotlib.pyplot as plt
import numpy as np
import os
import seaborn as sns
from matplotlib.gridspec import GridSpec

# 设置现代化的可视化风格
plt.style.use('default')
sns.set_palette("husl")

# 设置专业的字体和样式
plt.rcParams.update({
    'font.family': 'serif',
    'font.serif': ['Times New Roman', 'DejaVu Serif'],
    'font.size': 11,
    'axes.titlesize': 14,
    'axes.labelsize': 12,
    'xtick.labelsize': 10,
    'ytick.labelsize': 10,
    'legend.fontsize': 10,
    'figure.titlesize': 16,
    'axes.unicode_minus': False,
    'axes.grid': True,
    'grid.alpha': 0.3,
    'axes.spines.top': False,
    'axes.spines.right': False,
    'axes.linewidth': 1.2,
    'lines.linewidth': 2.5,
    'lines.markersize': 8
})

# 定义专业配色方案
COLORS = {
    'serial': '#2C3E50',      # 深蓝灰
    'openmp': '#27AE60',      # 绿色
    'mpi': '#E74C3C',         # 红色
    'ideal': '#F39C12',       # 橙色
    'background': '#FAFAFA',   # 背景色
    'grid': '#ECF0F1'         # 网格色
}

def create_sample_data():
    """创建示例性能数据"""
    vector_sizes = [100000, 500000, 1000000, 2000000]
    
    # 串行时间 (基准)
    serial_times = [0.0001, 0.0005, 0.001, 0.002]
    
    # OpenMP时间 (不同线程数)
    openmp_2_times = [0.00008, 0.0003, 0.0006, 0.0012]
    openmp_4_times = [0.00005, 0.00015, 0.0003, 0.0006]
    openmp_8_times = [0.00004, 0.00012, 0.00025, 0.0005]
    
    # MPI时间 (不同进程数)
    mpi_2_times = [0.00009, 0.00035, 0.0007, 0.0014]
    mpi_4_times = [0.00006, 0.0002, 0.0004, 0.0008]
    mpi_8_times = [0.00005, 0.00015, 0.0003, 0.0006]
    
    return {
        'vector_sizes': vector_sizes,
        'serial': serial_times,
        'openmp_2': openmp_2_times,
        'openmp_4': openmp_4_times,
        'openmp_8': openmp_8_times,
        'mpi_2': mpi_2_times,
        'mpi_4': mpi_4_times,
        'mpi_8': mpi_8_times
    }

def calculate_speedup(serial_times, parallel_times):
    """计算加速比"""
    return [s/p for s, p in zip(serial_times, parallel_times)]

def create_enhanced_performance_charts():
    """创建增强的性能分析图表"""
    data = create_sample_data()
    
    # 创建图形和子图布局
    fig = plt.figure(figsize=(16, 10))
    fig.patch.set_facecolor(COLORS['background'])
    
    # 使用GridSpec创建专业布局
    gs = GridSpec(2, 3, figure=fig, hspace=0.35, wspace=0.3,
                  left=0.08, right=0.95, top=0.92, bottom=0.08)
    
    # 计算加速比数据
    processes = [2, 4, 8]
    openmp_speedups = []
    mpi_speedups = []
    
    for p in processes:
        if p == 2:
            openmp_speedups.append(np.mean(calculate_speedup(data['serial'], data['openmp_2'])))
            mpi_speedups.append(np.mean(calculate_speedup(data['serial'], data['mpi_2'])))
        elif p == 4:
            openmp_speedups.append(np.mean(calculate_speedup(data['serial'], data['openmp_4'])))
            mpi_speedups.append(np.mean(calculate_speedup(data['serial'], data['mpi_4'])))
        elif p == 8:
            openmp_speedups.append(np.mean(calculate_speedup(data['serial'], data['openmp_8'])))
            mpi_speedups.append(np.mean(calculate_speedup(data['serial'], data['mpi_8'])))
    
    # 1. 执行时间对比 (左上)
    ax1 = fig.add_subplot(gs[0, 0])
    ax1.loglog(data['vector_sizes'], data['serial'], 'o-', 
               color=COLORS['serial'], label='Serial', linewidth=3, markersize=8)
    ax1.loglog(data['vector_sizes'], data['openmp_4'], 's-', 
               color=COLORS['openmp'], label='OpenMP (4 threads)', linewidth=3, markersize=8)
    ax1.loglog(data['vector_sizes'], data['mpi_4'], '^-', 
               color=COLORS['mpi'], label='MPI (4 processes)', linewidth=3, markersize=8)
    
    ax1.set_xlabel('Vector Size', fontweight='bold')
    ax1.set_ylabel('Execution Time (s)', fontweight='bold')
    ax1.set_title('Performance Comparison', fontweight='bold', pad=20)
    ax1.legend(frameon=True, fancybox=True, shadow=True, loc='upper left')
    ax1.grid(True, alpha=0.4, linestyle='--')
    
    # 2. 加速比分析 (中上)
    ax2 = fig.add_subplot(gs[0, 1])
    ax2.plot(processes, openmp_speedups, 'o-', 
             color=COLORS['openmp'], label='OpenMP', linewidth=3, markersize=10)
    ax2.plot(processes, mpi_speedups, 's-', 
             color=COLORS['mpi'], label='MPI', linewidth=3, markersize=10)
    ax2.plot(processes, processes, '--', 
             color=COLORS['ideal'], label='Ideal Speedup', linewidth=2.5, alpha=0.8)
    
    ax2.set_xlabel('Number of Processes/Threads', fontweight='bold')
    ax2.set_ylabel('Speedup', fontweight='bold')
    ax2.set_title('Speedup Analysis', fontweight='bold', pad=20)
    ax2.legend(frameon=True, fancybox=True, shadow=True)
    ax2.grid(True, alpha=0.4, linestyle='--')
    ax2.set_xlim(1.5, 8.5)
    ax2.set_ylim(0, 9)
    
    # 3. 并行效率分析 (右上)
    ax3 = fig.add_subplot(gs[0, 2])
    openmp_efficiency = [s/p for s, p in zip(openmp_speedups, processes)]
    mpi_efficiency = [s/p for s, p in zip(mpi_speedups, processes)]
    
    ax3.plot(processes, openmp_efficiency, 'o-', 
             color=COLORS['openmp'], label='OpenMP', linewidth=3, markersize=10)
    ax3.plot(processes, mpi_efficiency, 's-', 
             color=COLORS['mpi'], label='MPI', linewidth=3, markersize=10)
    ax3.axhline(y=1.0, color=COLORS['ideal'], linestyle='--', 
                label='Ideal Efficiency', linewidth=2.5, alpha=0.8)
    
    ax3.set_xlabel('Number of Processes/Threads', fontweight='bold')
    ax3.set_ylabel('Parallel Efficiency', fontweight='bold')
    ax3.set_title('Efficiency Analysis', fontweight='bold', pad=20)
    ax3.legend(frameon=True, fancybox=True, shadow=True)
    ax3.grid(True, alpha=0.4, linestyle='--')
    ax3.set_ylim(0, 1.1)
    ax3.set_xlim(1.5, 8.5)
    
    # 4. 综合性能对比 (下方跨列)
    ax4 = fig.add_subplot(gs[1, :])
    
    x_pos = np.arange(len(processes))
    width = 0.35
    
    bars1 = ax4.bar(x_pos - width/2, openmp_speedups, width, 
                    color=COLORS['openmp'], label='OpenMP', alpha=0.8, 
                    edgecolor='white', linewidth=1.5)
    bars2 = ax4.bar(x_pos + width/2, mpi_speedups, width, 
                    color=COLORS['mpi'], label='MPI', alpha=0.8, 
                    edgecolor='white', linewidth=1.5)
    
    # 添加数值标签
    for bars in [bars1, bars2]:
        for bar in bars:
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                    f'{height:.2f}', ha='center', va='bottom', 
                    fontweight='bold', fontsize=11)
    
    ax4.set_xlabel('Number of Processes/Threads', fontweight='bold')
    ax4.set_ylabel('Speedup', fontweight='bold')
    ax4.set_title('Comprehensive Performance Comparison', fontweight='bold', pad=20)
    ax4.set_xticks(x_pos)
    ax4.set_xticklabels([f'{p}' for p in processes])
    ax4.legend(frameon=True, fancybox=True, shadow=True, loc='upper left')
    ax4.grid(True, alpha=0.4, linestyle='--', axis='y')
    ax4.set_ylim(0, max(max(openmp_speedups), max(mpi_speedups)) * 1.2)
    
    # 保存图表
    output_dir = 'results'
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    plt.savefig(os.path.join(output_dir, 'enhanced_performance_analysis.png'), 
                dpi=300, bbox_inches='tight', facecolor=COLORS['background'])
    print(f"Enhanced performance chart saved to: {output_dir}/enhanced_performance_analysis.png")
    
    plt.show()

def create_algorithm_architecture_diagram():
    """创建算法架构对比图"""
    fig, axes = plt.subplots(1, 3, figsize=(18, 8))
    fig.patch.set_facecolor(COLORS['background'])
    
    # 算法信息
    algorithms = [
        {
            'title': 'Serial Algorithm',
            'description': 'Sequential dot product computation',
            'complexity': 'Time: O(n), Space: O(1)',
            'features': ['Single thread execution', 'Simple implementation', 'Memory efficient'],
            'color': COLORS['serial']
        },
        {
            'title': 'OpenMP Parallel',
            'description': 'Shared-memory parallel computation',
            'complexity': 'Time: O(n/p), Space: O(1)',
            'features': ['Multi-thread execution', 'Automatic load balancing', 'Reduction operation'],
            'color': COLORS['openmp']
        },
        {
            'title': 'MPI Distributed',
            'description': 'Distributed-memory parallel computation',
            'complexity': 'Time: O(n/p + log p), Space: O(n/p)',
            'features': ['Multi-process execution', 'Data distribution', 'Collective communication'],
            'color': COLORS['mpi']
        }
    ]
    
    for i, (ax, algo) in enumerate(zip(axes, algorithms)):
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        
        # 标题
        ax.text(0.5, 0.9, algo['title'], ha='center', va='center', 
                fontsize=16, fontweight='bold', color=algo['color'])
        
        # 描述
        ax.text(0.5, 0.8, algo['description'], ha='center', va='center', 
                fontsize=12, style='italic')
        
        # 复杂度
        ax.text(0.5, 0.65, algo['complexity'], ha='center', va='center', 
                fontsize=11, fontweight='bold', 
                bbox=dict(boxstyle="round,pad=0.3", facecolor=algo['color'], alpha=0.2))
        
        # 特性
        for j, feature in enumerate(algo['features']):
            ax.text(0.5, 0.45 - j*0.08, f"• {feature}", ha='center', va='center', 
                    fontsize=10)
        
        ax.set_title(f"Algorithm {i+1}", fontweight='bold', pad=20)
        ax.axis('off')
        
        # 添加边框
        for spine in ax.spines.values():
            spine.set_visible(True)
            spine.set_linewidth(2)
            spine.set_edgecolor(algo['color'])
    
    plt.tight_layout()
    
    # 保存图表
    output_dir = 'results'
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    plt.savefig(os.path.join(output_dir, 'algorithm_architecture.png'), 
                dpi=300, bbox_inches='tight', facecolor=COLORS['background'])
    print(f"Algorithm architecture diagram saved to: {output_dir}/algorithm_architecture.png")
    
    plt.show()

if __name__ == '__main__':
    print("=== Enhanced Dot Product Performance Visualization ===")
    print("Generating enhanced performance analysis charts...")
    
    try:
        create_enhanced_performance_charts()
        create_algorithm_architecture_diagram()
        print("\nEnhanced visualization charts generated successfully!")
    except Exception as e:
        print(f"Error generating charts: {e}")
        print("Please ensure matplotlib and seaborn are installed: pip3 install matplotlib seaborn")
