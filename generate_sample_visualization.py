#!/usr/bin/env python3
"""
点积运算性能可视化优化脚本
Enhanced visualization script for dot product performance analysis
"""

import matplotlib.pyplot as plt
import numpy as np
import os
import seaborn as sns
from matplotlib.patches import Rectangle
import matplotlib.patches as mpatches

# 设置现代化的可视化风格
plt.style.use('seaborn-v0_8-whitegrid')
sns.set_palette("husl")

# 设置字体和样式
plt.rcParams.update({
    'font.family': 'serif',
    'font.serif': ['Times New Roman', 'DejaVu Serif'],
    'font.size': 11,
    'axes.titlesize': 14,
    'axes.labelsize': 12,
    'xtick.labelsize': 10,
    'ytick.labelsize': 10,
    'legend.fontsize': 10,
    'figure.titlesize': 16,
    'axes.unicode_minus': False,
    'axes.grid': True,
    'grid.alpha': 0.3,
    'axes.spines.top': False,
    'axes.spines.right': False,
    'axes.linewidth': 0.8
})

# 定义专业配色方案
COLORS = {
    'serial': '#2E4057',      # 深蓝灰
    'openmp': '#048A81',      # 青绿色
    'mpi': '#F18F01',         # 橙色
    'ideal': '#C73E1D',       # 红色
    'grid': '#E8E8E8',        # 浅灰
    'background': '#FAFAFA'   # 背景色
}

def create_sample_data():
    """创建示例性能数据"""
    # 向量大小
    vector_sizes = [100000, 500000, 1000000, 2000000]
    
    # 串行时间 (基准)
    serial_times = [0.0001, 0.0005, 0.001, 0.002]
    
    # OpenMP时间 (不同线程数)
    openmp_2_times = [0.00008, 0.0003, 0.0006, 0.0012]
    openmp_4_times = [0.00006, 0.00015, 0.0003, 0.0006]
    openmp_8_times = [0.00005, 0.00012, 0.00025, 0.0005]
    
    # MPI时间 (不同进程数)
    mpi_2_times = [0.00009, 0.00035, 0.0007, 0.0014]
    mpi_4_times = [0.00007, 0.0002, 0.0004, 0.0008]
    mpi_8_times = [0.00006, 0.00015, 0.0003, 0.0006]
    
    return {
        'vector_sizes': vector_sizes,
        'serial': serial_times,
        'openmp_2': openmp_2_times,
        'openmp_4': openmp_4_times,
        'openmp_8': openmp_8_times,
        'mpi_2': mpi_2_times,
        'mpi_4': mpi_4_times,
        'mpi_8': mpi_8_times
    }

def calculate_speedup(serial_times, parallel_times):
    """计算加速比"""
    return [s/p for s, p in zip(serial_times, parallel_times)]

def create_performance_charts():
    """创建优化的性能分析图表"""
    data = create_sample_data()

    # 创建紧凑的图表布局
    fig = plt.figure(figsize=(14, 10))
    fig.patch.set_facecolor(COLORS['background'])

    # 使用GridSpec创建更灵活的布局
    gs = fig.add_gridspec(2, 3, hspace=0.3, wspace=0.3,
                         left=0.08, right=0.95, top=0.92, bottom=0.08)

    # 1. 执行时间对比 (占据左上角)
    ax1 = fig.add_subplot(gs[0, 0])
    ax1.loglog(data['vector_sizes'], data['serial'], 'o-',
               color=COLORS['serial'], label='Serial', linewidth=2.5, markersize=7)
    ax1.loglog(data['vector_sizes'], data['openmp_4'], 's-',
               color=COLORS['openmp'], label='OpenMP (4 threads)', linewidth=2.5, markersize=7)
    ax1.loglog(data['vector_sizes'], data['mpi_4'], '^-',
               color=COLORS['mpi'], label='MPI (4 processes)', linewidth=2.5, markersize=7)

    ax1.set_xlabel('Vector Size', fontweight='bold')
    ax1.set_ylabel('Execution Time (s)', fontweight='bold')
    ax1.set_title('Performance Comparison', fontweight='bold', pad=15)
    ax1.legend(frameon=True, fancybox=True, shadow=True)
    ax1.grid(True, alpha=0.3, linestyle='--')
    
    # 2. 加速比分析
    processes = [2, 4, 8]
    openmp_speedups = []
    mpi_speedups = []
    
    for i, p in enumerate(processes):
        if p == 2:
            openmp_speedups.append(np.mean(calculate_speedup(data['serial'], data['openmp_2'])))
            mpi_speedups.append(np.mean(calculate_speedup(data['serial'], data['mpi_2'])))
        elif p == 4:
            openmp_speedups.append(np.mean(calculate_speedup(data['serial'], data['openmp_4'])))
            mpi_speedups.append(np.mean(calculate_speedup(data['serial'], data['mpi_4'])))
        elif p == 8:
            openmp_speedups.append(np.mean(calculate_speedup(data['serial'], data['openmp_8'])))
            mpi_speedups.append(np.mean(calculate_speedup(data['serial'], data['mpi_8'])))
    
    ax2.plot(processes, openmp_speedups, 'o-', label='OpenMP', linewidth=2, markersize=8)
    ax2.plot(processes, mpi_speedups, 's-', label='MPI', linewidth=2, markersize=8)
    ax2.plot(processes, processes, '--', color='gray', label='理想加速比')
    
    ax2.set_xlabel('进程/线程数')
    ax2.set_ylabel('加速比')
    ax2.set_title('加速比分析')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 并行效率
    openmp_efficiency = [s/p for s, p in zip(openmp_speedups, processes)]
    mpi_efficiency = [s/p for s, p in zip(mpi_speedups, processes)]
    
    ax3.plot(processes, openmp_efficiency, 'o-', label='OpenMP', linewidth=2, markersize=8)
    ax3.plot(processes, mpi_efficiency, 's-', label='MPI', linewidth=2, markersize=8)
    ax3.axhline(y=1.0, color='gray', linestyle='--', label='理想效率')
    
    ax3.set_xlabel('进程/线程数')
    ax3.set_ylabel('并行效率')
    ax3.set_title('并行效率分析')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    ax3.set_ylim(0, 1.2)
    
    # 4. 性能对比柱状图
    methods = ['OpenMP (4线程)', 'MPI (4进程)']
    best_speedups = [openmp_speedups[1], mpi_speedups[1]]  # 4进程/线程的结果
    
    bars = ax4.bar(methods, best_speedups, color=['skyblue', 'lightcoral'])
    ax4.set_ylabel('加速比')
    ax4.set_title('最佳性能对比 (4进程/线程)')
    ax4.grid(True, alpha=0.3, axis='y')
    
    # 在柱状图上添加数值标签
    for bar, speedup in zip(bars, best_speedups):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + 0.05,
                f'{speedup:.2f}', ha='center', va='bottom')
    
    plt.tight_layout()
    
    # 保存图表
    output_dir = 'results'
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    plt.savefig(os.path.join(output_dir, 'performance_analysis.png'), 
                dpi=300, bbox_inches='tight')
    print(f"性能分析图表已保存到: {output_dir}/performance_analysis.png")
    
    # 显示图表
    plt.show()

def create_algorithm_diagram():
    """创建算法流程图"""
    fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(18, 6))
    
    # 串行算法
    ax1.text(0.5, 0.9, '串行点积算法', ha='center', va='center', fontsize=14, fontweight='bold')
    ax1.text(0.5, 0.7, 'for i = 0 to n-1:', ha='center', va='center', fontsize=12)
    ax1.text(0.5, 0.6, '    sum += a[i] * b[i]', ha='center', va='center', fontsize=12)
    ax1.text(0.5, 0.4, '时间复杂度: O(n)', ha='center', va='center', fontsize=12)
    ax1.text(0.5, 0.3, '空间复杂度: O(1)', ha='center', va='center', fontsize=12)
    ax1.set_xlim(0, 1)
    ax1.set_ylim(0, 1)
    ax1.set_title('串行实现')
    ax1.axis('off')
    
    # OpenMP算法
    ax2.text(0.5, 0.9, 'OpenMP并行算法', ha='center', va='center', fontsize=14, fontweight='bold')
    ax2.text(0.5, 0.7, '#pragma omp parallel for', ha='center', va='center', fontsize=12)
    ax2.text(0.5, 0.6, 'reduction(+:sum)', ha='center', va='center', fontsize=12)
    ax2.text(0.5, 0.5, 'for i = 0 to n-1:', ha='center', va='center', fontsize=12)
    ax2.text(0.5, 0.4, '    sum += a[i] * b[i]', ha='center', va='center', fontsize=12)
    ax2.text(0.5, 0.2, '共享内存并行', ha='center', va='center', fontsize=12)
    ax2.set_xlim(0, 1)
    ax2.set_ylim(0, 1)
    ax2.set_title('OpenMP实现')
    ax2.axis('off')
    
    # MPI算法
    ax3.text(0.5, 0.9, 'MPI分布式算法', ha='center', va='center', fontsize=14, fontweight='bold')
    ax3.text(0.5, 0.7, '1. 数据分发 (Scatter)', ha='center', va='center', fontsize=12)
    ax3.text(0.5, 0.6, '2. 局部计算', ha='center', va='center', fontsize=12)
    ax3.text(0.5, 0.5, '3. 结果归约 (Reduce)', ha='center', va='center', fontsize=12)
    ax3.text(0.5, 0.3, '分布式内存并行', ha='center', va='center', fontsize=12)
    ax3.text(0.5, 0.2, '适用于大规模集群', ha='center', va='center', fontsize=12)
    ax3.set_xlim(0, 1)
    ax3.set_ylim(0, 1)
    ax3.set_title('MPI实现')
    ax3.axis('off')
    
    plt.tight_layout()
    
    # 保存算法图
    output_dir = 'results'
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    plt.savefig(os.path.join(output_dir, 'algorithm_comparison.png'), 
                dpi=300, bbox_inches='tight')
    print(f"算法对比图已保存到: {output_dir}/algorithm_comparison.png")
    
    plt.show()

if __name__ == '__main__':
    print("=== 点积运算性能可视化 ===")
    print("Generating performance analysis charts...")
    
    try:
        create_performance_charts()
        create_algorithm_diagram()
        print("\n可视化图表生成完成!")
        print("Visualization charts generated successfully!")
    except Exception as e:
        print(f"Error generating charts: {e}")
        print("请确保安装了matplotlib: pip3 install matplotlib")
